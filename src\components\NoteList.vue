<!-- src/components/NoteList.vue -->
<template>
  <div class="note-list">
    <div class="note-list-header">
      <h2>我的笔记</h2>
      <button @click="handleCreateNote" class="create-btn" title="新建笔记">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"></line>
          <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
      </button>
    </div>
    
    <div class="notes-container">
      <div v-if="notes.length === 0" class="empty-state">
        <p>还没有笔记</p>
        <p class="empty-hint">点击 + 按钮创建第一个笔记</p>
      </div>
      
      <div
        v-for="note in notes"
        :key="note.id"
        :class="['note-item', { active: note.id === currentNoteId }]"
        @click="handleSelectNote(note.id)"
      >
        <div class="note-title">{{ note.title || '无标题' }}</div>
        <div class="note-preview">{{ getPreview(note.content) }}</div>
        <div class="note-date">{{ formatDate(note.createdAt) }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useNotes } from '../composables/useNotes.js'

const { notes, currentNoteId, createNote, selectNote } = useNotes()

const handleCreateNote = () => {
  createNote()
}

const handleSelectNote = (id) => {
  selectNote(id)
}

const getPreview = (content) => {
  if (!content) return '无内容'
  return content.length > 50 ? content.substring(0, 50) + '...' : content
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return '今天'
  } else if (diffDays === 2) {
    return '昨天'
  } else if (diffDays <= 7) {
    return `${diffDays - 1}天前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric'
    })
  }
}
</script>

<style scoped>
.note-list {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
}

.note-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e9ecef;
  background: white;
}

.note-list-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #343a40;
}

.create-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.create-btn:hover {
  background: #0056b3;
  transform: scale(1.05);
}

.notes-container {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  text-align: center;
}

.empty-state p {
  margin: 0.25rem 0;
}

.empty-hint {
  font-size: 0.875rem;
  opacity: 0.7;
}

.note-item {
  padding: 1rem;
  margin: 0 0.5rem 0.5rem 0.5rem;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.note-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.note-item.active {
  border-color: #007bff;
  box-shadow: 0 2px 12px rgba(0, 123, 255, 0.15);
}

.note-title {
  font-weight: 600;
  color: #343a40;
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  line-height: 1.3;
}

.note-preview {
  color: #6c757d;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.note-date {
  color: #adb5bd;
  font-size: 0.75rem;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .note-list-header {
    padding: 0.75rem;
  }
  
  .note-list-header h2 {
    font-size: 1.1rem;
  }
  
  .create-btn {
    width: 32px;
    height: 32px;
  }
  
  .note-item {
    padding: 0.75rem;
    margin: 0 0.25rem 0.5rem 0.25rem;
  }
  
  .note-title {
    font-size: 0.9rem;
  }
  
  .note-preview {
    font-size: 0.8rem;
  }
}
</style>
