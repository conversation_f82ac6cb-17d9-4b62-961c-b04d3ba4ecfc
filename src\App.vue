<!-- src/App.vue -->
<script setup>
import NoteList from './components/NoteList.vue'
import NoteEditor from './components/NoteEditor.vue'
</script>

<template>
  <div class="app">
    <div class="app-container">
      <aside class="sidebar">
        <NoteList />
      </aside>
      <main class="main-content">
        <NoteEditor />
      </main>
    </div>
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
}

#app {
  height: 100vh;
}
</style>

<style scoped>
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.sidebar {
  width: 320px;
  min-width: 280px;
  max-width: 400px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 40vh;
    min-width: unset;
    max-width: unset;
    border-right: none;
    border-bottom: 1px solid #e9ecef;
  }

  .main-content {
    height: 60vh;
  }
}

@media (max-width: 480px) {
  .sidebar {
    height: 35vh;
  }

  .main-content {
    height: 65vh;
  }
}
</style>
