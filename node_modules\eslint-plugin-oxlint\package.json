{"name": "eslint-plugin-oxlint", "version": "0.16.12", "description": "Turn off all rules already supported by oxlint", "type": "module", "types": "./dist/index.d.ts", "packageManager": "pnpm@10.11.0", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}, "./rules-by-category": {"types": "./dist/generated/rules-by-category.d.ts", "import": "./dist/generated/rules-by-category.mjs", "require": "./dist/generated/rules-by-category.cjs", "default": "./dist/generated/rules-by-category.mjs"}, "./rules-by-scope": {"types": "./dist/generated/rules-by-scope.d.ts", "import": "./dist/generated/rules-by-scope.mjs", "require": "./dist/generated/rules-by-scope.cjs", "default": "./dist/generated/rules-by-scope.mjs"}, "./package.json": "./package.json"}, "files": ["dist", "src"], "author": "Dunqing <<EMAIL>>", "homepage": "https://github.com/oxc-project/eslint-plugin-oxlint", "repository": {"type": "git", "url": "**************:oxc-project/eslint-plugin-oxlint.git"}, "license": "MIT", "scripts": {"prepare": "husky", "generate": "node --import @oxc-node/core/register ./scripts/generate.ts", "build": "vite build", "lint": "npx oxlint --tsconfig=tsconfig.json && npx eslint", "format": "npx prettier --write .", "type-check": "tsc --noEmit", "test": "vitest --reporter=verbose"}, "keywords": ["oxc", "ox<PERSON>", "eslint", "rules"], "devDependencies": {"@eslint/js": "^9.13.0", "@oxc-node/core": "^0.0.27", "@types/eslint-config-prettier": "^6.11.3", "@types/node": "^22.7.7", "@types/shelljs": "^0.8.15", "@vitest/coverage-v8": "^3.0.0", "dedent": "^1.5.3", "eslint": "^9.13.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-unicorn": "^59.0.0", "husky": "^9.1.6", "jiti": "^2.4.2", "lint-staged": "^16.0.0", "memfs": "^4.14.0", "oxlint": "^0.16.12", "prettier": "^3.3.3", "scule": "^1.3.0", "shelljs": "^0.10.0", "typescript": "^5.6.3", "typescript-eslint": "^8.10.0", "vite": "^6.0.0", "vite-plugin-dts": "^4.2.4", "vitest": "^3.0.0"}, "lint-staged": {"*.{js,cjs,ts}": "eslint", "*": "prettier --ignore-unknown --write"}, "volta": {"node": "20.14.0"}, "dependencies": {"jsonc-parser": "^3.3.1"}}