<!-- src/components/NoteEditor.vue -->
<template>
  <div class="note-editor">
    <div v-if="!currentNote" class="empty-editor">
      <div class="empty-content">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
          <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
          <polyline points="10,9 9,9 8,9"/>
        </svg>
        <h3>选择一个笔记开始编辑</h3>
        <p>从左侧列表中选择笔记，或创建一个新笔记</p>
      </div>
    </div>
    
    <div v-else class="editor-content">
      <div class="editor-header">
        <input
          v-model="localTitle"
          @input="handleTitleChange"
          class="title-input"
          placeholder="输入笔记标题..."
          maxlength="100"
        />
        <div class="editor-actions">
          <span class="save-status" :class="{ saving: isSaving }">
            {{ saveStatus }}
          </span>
          <button @click="handleDelete" class="delete-btn" title="删除笔记">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="3,6 5,6 21,6"/>
              <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
              <line x1="10" y1="11" x2="10" y2="17"/>
              <line x1="14" y1="11" x2="14" y2="17"/>
            </svg>
          </button>
        </div>
      </div>
      
      <div class="editor-body">
        <textarea
          v-model="localContent"
          @input="handleContentChange"
          class="content-textarea"
          placeholder="开始写下你的想法..."
          ref="contentTextarea"
        ></textarea>
      </div>
      
      <div class="editor-footer">
        <div class="note-info">
          <span>创建于 {{ formatDate(currentNote.createdAt) }}</span>
          <span class="separator">•</span>
          <span>{{ getWordCount(localContent) }} 字</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, nextTick } from 'vue'
import { useNotes } from '../composables/useNotes.js'

const { currentNote, updateNote, deleteNote } = useNotes()

// 本地状态
const localTitle = ref('')
const localContent = ref('')
const isSaving = ref(false)
const saveStatus = ref('已保存')
const contentTextarea = ref(null)

// 防抖保存
let saveTimeout = null
const debouncedSave = (field, value) => {
  isSaving.value = true
  saveStatus.value = '保存中...'
  
  clearTimeout(saveTimeout)
  saveTimeout = setTimeout(() => {
    if (currentNote.value) {
      updateNote(currentNote.value.id, { [field]: value })
      isSaving.value = false
      saveStatus.value = '已保存'
    }
  }, 500)
}

// 处理标题变化
const handleTitleChange = () => {
  debouncedSave('title', localTitle.value)
}

// 处理内容变化
const handleContentChange = () => {
  debouncedSave('content', localContent.value)
}

// 处理删除
const handleDelete = () => {
  if (currentNote.value && confirm('确定要删除这个笔记吗？此操作无法撤销。')) {
    deleteNote(currentNote.value.id)
  }
}

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取字数
const getWordCount = (text) => {
  if (!text) return 0
  return text.length
}

// 监听当前笔记变化，同步本地状态
watch(currentNote, (newNote) => {
  if (newNote) {
    localTitle.value = newNote.title || ''
    localContent.value = newNote.content || ''
    saveStatus.value = '已保存'
    
    // 自动聚焦到内容区域
    nextTick(() => {
      if (contentTextarea.value) {
        contentTextarea.value.focus()
      }
    })
  } else {
    localTitle.value = ''
    localContent.value = ''
  }
}, { immediate: true })
</script>

<style scoped>
.note-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.empty-editor {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  color: #6c757d;
  max-width: 300px;
}

.empty-content svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.empty-content p {
  margin: 0;
  font-size: 0.95rem;
  opacity: 0.8;
}

.editor-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.title-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 1.5rem;
  font-weight: 600;
  color: #343a40;
  outline: none;
  padding: 0.5rem 0;
}

.title-input::placeholder {
  color: #adb5bd;
}

.editor-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.save-status {
  font-size: 0.875rem;
  color: #28a745;
  font-weight: 500;
  transition: color 0.2s ease;
}

.save-status.saving {
  color: #ffc107;
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background: #dc3545;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background: #c82333;
  transform: scale(1.05);
}

.editor-body {
  flex: 1;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
}

.content-textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  font-size: 1rem;
  line-height: 1.6;
  color: #343a40;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.content-textarea::placeholder {
  color: #adb5bd;
}

.editor-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.note-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.separator {
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    padding: 1rem;
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .title-input {
    font-size: 1.25rem;
  }
  
  .editor-actions {
    justify-content: space-between;
  }
  
  .editor-body {
    padding: 1rem;
  }
  
  .editor-footer {
    padding: 1rem;
  }
  
  .note-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .separator {
    display: none;
  }
}
</style>
