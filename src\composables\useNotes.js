// src/composables/useNotes.js
import { ref, computed, watch } from 'vue'

const STORAGE_KEY = 'notes-app-data'

// 全局状态
const notes = ref([])
const currentNoteId = ref(null)

// 从localStorage加载数据
const loadNotes = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (stored) {
      const data = JSON.parse(stored)
      notes.value = data.notes || []
      currentNoteId.value = data.currentNoteId || null
    }
  } catch (error) {
    console.error('Failed to load notes from localStorage:', error)
    notes.value = []
    currentNoteId.value = null
  }
}

// 保存数据到localStorage
const saveNotes = () => {
  try {
    const data = {
      notes: notes.value,
      currentNoteId: currentNoteId.value
    }
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
  } catch (error) {
    console.error('Failed to save notes to localStorage:', error)
  }
}

// 创建新笔记
const createNote = () => {
  const newNote = {
    id: Date.now().toString(),
    title: '新建笔记',
    content: '',
    createdAt: new Date().toISOString()
  }
  
  notes.value.unshift(newNote)
  currentNoteId.value = newNote.id
  saveNotes()
  
  return newNote
}

// 更新笔记
const updateNote = (id, updates) => {
  const noteIndex = notes.value.findIndex(note => note.id === id)
  if (noteIndex !== -1) {
    notes.value[noteIndex] = { ...notes.value[noteIndex], ...updates }
    saveNotes()
  }
}

// 删除笔记
const deleteNote = (id) => {
  const noteIndex = notes.value.findIndex(note => note.id === id)
  if (noteIndex !== -1) {
    notes.value.splice(noteIndex, 1)
    
    // 如果删除的是当前选中的笔记，选择下一个笔记
    if (currentNoteId.value === id) {
      if (notes.value.length > 0) {
        // 选择删除位置的下一个笔记，如果没有则选择前一个
        const nextIndex = noteIndex < notes.value.length ? noteIndex : noteIndex - 1
        currentNoteId.value = nextIndex >= 0 ? notes.value[nextIndex].id : null
      } else {
        currentNoteId.value = null
      }
    }
    
    saveNotes()
  }
}

// 选择笔记
const selectNote = (id) => {
  currentNoteId.value = id
  saveNotes()
}

// 计算属性
const currentNote = computed(() => {
  return notes.value.find(note => note.id === currentNoteId.value) || null
})

const sortedNotes = computed(() => {
  return [...notes.value].sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
})

// 组合式函数
export function useNotes() {
  // 初始化时加载数据
  if (notes.value.length === 0) {
    loadNotes()
  }
  
  // 监听数据变化并自动保存
  watch([notes, currentNoteId], saveNotes, { deep: true })
  
  return {
    // 状态
    notes: sortedNotes,
    currentNote,
    currentNoteId,
    
    // 方法
    createNote,
    updateNote,
    deleteNote,
    selectNote,
    loadNotes,
    saveNotes
  }
}
